import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Eateries preferences
 */
export interface EateriesPreferences {
  cuisineTypes: string[]
  diningExperience: string[]
  dietaryNeeds: string[]
}

/**
 * Shopping preferences
 */
export interface ShoppingPreferences {
  style: string[]
  budget: string
  focusAreas: string[]
}

/**
 * Entertainment preferences
 */
export interface EntertainmentPreferences {
  venues: string[]
  vibe: string[]
  interests: string[]
}

/**
 * Activity preferences entity
 */
export interface ActivityPreferences extends BaseEntity {
  userId: string
  eateries: EateriesPreferences
  shopping: ShoppingPreferences
  entertainment: EntertainmentPreferences
}

/**
 * Activity preferences create data (without BaseEntity fields)
 */
export interface ActivityPreferencesCreateData {
  userId: string
  eateries: EateriesPreferences
  shopping: ShoppingPreferences
  entertainment: EntertainmentPreferences
}

/**
 * Activity preferences update data (partial)
 */
export interface ActivityPreferencesUpdateData {
  eateries?: Partial<EateriesPreferences>
  shopping?: Partial<ShoppingPreferences>
  entertainment?: Partial<EntertainmentPreferences>
}

/**
 * Default activity preferences structure
 */
export const DEFAULT_ACTIVITY_PREFERENCES: Omit<ActivityPreferences, keyof BaseEntity | 'userId'> = {
  eateries: {
    cuisineTypes: [],
    diningExperience: [],
    dietaryNeeds: [],
  },
  shopping: {
    style: [],
    budget: 'moderate',
    focusAreas: [],
  },
  entertainment: {
    venues: [],
    vibe: [],
    interests: [],
  },
}

/**
 * Available preference options
 */
export const PREFERENCE_OPTIONS = {
  eateries: {
    cuisineTypes: [
      'Italian',
      'Mexican',
      'Asian',
      'American',
      'Mediterranean',
      'French',
      'Indian',
      'Thai',
      'Japanese',
      'Chinese',
      'Greek',
      'Spanish',
      'Middle Eastern',
      'Seafood',
      'Steakhouse',
      'Vegetarian',
      'Vegan',
    ],
    diningExperience: [
      'Fine Dining',
      'Casual Dining',
      'Fast Casual',
      'Food Trucks',
      'Local Favorites',
      'Rooftop/Views',
      'Outdoor Seating',
      'Live Music',
      'Family-Friendly',
      'Romantic',
      'Group-Friendly',
    ],
    dietaryNeeds: [
      'Vegetarian',
      'Vegan',
      'Gluten-Free',
      'Dairy-Free',
      'Nut-Free',
      'Keto',
      'Paleo',
      'Halal',
      'Kosher',
      'Low-Carb',
      'Organic',
    ],
  },
  shopping: {
    style: [
      'Luxury Brands',
      'Vintage/Thrift',
      'Local Artisans',
      'Designer Outlets',
      'Department Stores',
      'Boutique Shopping',
      'Street Markets',
      'Antiques',
    ],
    budget: [
      'budget',
      'moderate',
      'luxury',
    ],
    focusAreas: [
      'Fashion/Clothing',
      'Accessories',
      'Home Decor',
      'Art/Crafts',
      'Souvenirs',
      'Electronics',
      'Books',
      'Jewelry',
      'Shoes',
      'Beauty Products',
    ],
  },
  entertainment: {
    venues: [
      'Museums',
      'Art Galleries',
      'Live Music Venues',
      'Theaters',
      'Comedy Clubs',
      'Bars/Nightlife',
      'Casinos',
      'Sports Events',
      'Outdoor Activities',
      'Adventure Sports',
      'Spas/Wellness',
      'Tours/Experiences',
    ],
    vibe: [
      'Relaxed',
      'Energetic',
      'Cultural',
      'Adventurous',
      'Romantic',
      'Social/Group',
      'Solo-Friendly',
      'Family-Oriented',
      'Nightlife',
      'Daytime',
      'Educational',
      'Thrilling',
    ],
    interests: [
      'History',
      'Art',
      'Music',
      'Sports',
      'Nature',
      'Photography',
      'Food & Drink',
      'Architecture',
      'Science',
      'Technology',
      'Fashion',
      'Wellness',
      'Adventure',
      'Culture',
    ],
  },
} as const
