"use client"

import React, { useCallback } from "react"
import { useRouter } from "next/navigation"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Crown, Sparkles, Utensils, ShoppingBag, Music } from "lucide-react"

interface ActivityPreferencesWelcomeModalProps {
  isOpen: boolean
  onClose: () => void
}

/**
 * Welcome modal for new Pro subscribers to set up activity preferences
 */
export const ActivityPreferencesWelcomeModal = React.memo<ActivityPreferencesWelcomeModalProps>(
  ({ isOpen, onClose }) => {
    const router = useRouter()

    const handleSetupPreferences = useCallback(() => {
      onClose()
      // Navigate to settings with activity preferences tab active
      router.push("/settings?tab=activity-preferences")
    }, [onClose, router])

    const handleSkipForNow = useCallback(() => {
      onClose()
    }, [onClose])

    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-yellow-400 to-orange-500">
              <Crown className="h-8 w-8 text-white" />
            </div>
            <DialogTitle className="text-xl font-bold">
              Congrats! Now You Can Customize Your Itinerary Like a Pro
            </DialogTitle>
            <DialogDescription className="text-center space-y-4">
              <p>
                Welcome to Pro! You now have access to personalized activity preferences that will make your itinerary suggestions even better.
              </p>
              
              <div className="grid grid-cols-3 gap-4 py-4">
                <div className="text-center">
                  <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
                    <Utensils className="h-5 w-5 text-red-600" />
                  </div>
                  <p className="text-xs font-medium">Dining</p>
                </div>
                <div className="text-center">
                  <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                    <ShoppingBag className="h-5 w-5 text-blue-600" />
                  </div>
                  <p className="text-xs font-medium">Shopping</p>
                </div>
                <div className="text-center">
                  <div className="mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-purple-100">
                    <Music className="h-5 w-5 text-purple-600" />
                  </div>
                  <p className="text-xs font-medium">Entertainment</p>
                </div>
              </div>
              
              <p className="text-sm">
                Set up your preferences now to start getting personalized suggestions, or you can always do this later in your settings.
              </p>
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col gap-3 pt-4">
            <Button 
              onClick={handleSetupPreferences}
              className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white"
            >
              <Sparkles className="mr-2 h-4 w-4" />
              Set Up My Preferences
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleSkipForNow}
              className="w-full"
            >
              Skip for Now
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }
)

ActivityPreferencesWelcomeModal.displayName = "ActivityPreferencesWelcomeModal"
